from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django import forms
from django.db import connections
from decimal import Decimal
import json
from .models import Order, OrderItem, OrderStatusHistory, OrderCancellation, OrderReschedule


class OrderAdminForm(forms.ModelForm):
    """
    Enhanced custom form for Order admin with service selection, tax configuration,
    coupon application, and payment link generation
    """
    # Coupon application
    apply_coupon = forms.CharField(
        max_length=50,
        required=False,
        help_text="Enter coupon code to apply discount"
    )

    # Payment method selection (default COD)
    generate_payment_link = forms.BooleanField(
        required=False,
        initial=False,
        help_text="Generate Razorpay payment link for customer (order defaults to COD)"
    )

    # Auto-calculate totals
    auto_calculate_tax = forms.BooleanField(
        required=False,
        initial=True,
        help_text="Automatically calculate tax based on subtotal"
    )

    # Add tax configuration selection
    tax_configuration = forms.ModelChoiceField(
        queryset=None,
        required=False,
        empty_label="Use Default Tax Configuration",
        help_text="Select specific tax configuration for this order (leave empty for default)"
    )

    # Add tax override options
    override_tax_calculation = forms.BooleanField(
        required=False,
        help_text="Check to manually override tax calculation"
    )

    manual_tax_amount = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        required=False,
        help_text="Manual tax amount (only if override is checked)"
    )

    tax_exemption_reason = forms.CharField(
        max_length=200,
        required=False,
        help_text="Reason for tax exemption or manual override"
    )

    class Meta:
        model = Order
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Populate tax configuration choices
        try:
            from taxation.models import TaxConfiguration
            self.fields['tax_configuration'].queryset = TaxConfiguration.objects.filter(is_active=True)
        except ImportError:
            # Remove field if taxation app is not available
            del self.fields['tax_configuration']

    def save(self, commit=True):
        order = super().save(commit=False)

        # Set default payment method to COD
        if not order.payment_method:
            order.payment_method = 'cod'
            order.payment_status = 'pending'

        # Handle coupon application
        coupon_code = self.cleaned_data.get('apply_coupon')
        if coupon_code:
            try:
                # Import here to avoid circular imports
                from coupons.models import Coupon
                coupon = Coupon.objects.get(code=coupon_code.upper(), is_active=True)

                if coupon.is_valid():
                    # Calculate discount
                    discount_amount = coupon.calculate_discount(order.subtotal)
                    order.coupon_code = coupon_code.upper()
                    order.coupon_discount = discount_amount
                    order.discount_amount = discount_amount
            except Exception as e:
                # Add error message (in a real implementation, you'd want to show this to the user)
                pass

        # Handle automatic tax calculation
        if self.cleaned_data.get('auto_calculate_tax') and not self.cleaned_data.get('override_tax_calculation'):
            try:
                from taxation.services import TaxCalculationService

                # Calculate tax on subtotal after discount
                taxable_amount = order.subtotal - order.discount_amount
                tax_calculation = TaxCalculationService.calculate_total_tax_and_charges(taxable_amount)

                order.tax_amount = tax_calculation['total_gst']
                # Store individual tax components if available
                if hasattr(order, 'cgst_amount'):
                    order.cgst_amount = tax_calculation.get('cgst', Decimal('0.00'))
                    order.sgst_amount = tax_calculation.get('sgst', Decimal('0.00'))
                    order.igst_amount = tax_calculation.get('igst', Decimal('0.00'))

            except Exception as e:
                # Fallback to manual calculation or default
                pass

        # Handle tax configuration override
        if self.cleaned_data.get('override_tax_calculation'):
            manual_tax = self.cleaned_data.get('manual_tax_amount', 0)
            if manual_tax:
                order.tax_amount = manual_tax

        # Calculate final total
        order.total_amount = order.subtotal + order.tax_amount - order.discount_amount + order.minimum_order_fee

        if commit:
            order.save()

            # Generate payment link if requested
            if self.cleaned_data.get('generate_payment_link'):
                self._generate_payment_link(order)

        return order

    def _generate_payment_link(self, order):
        """Generate Razorpay payment link for the order"""
        try:
            from payments.models import PaymentTransaction, PaymentConfiguration
            import razorpay

            # Get payment configuration
            payment_config = PaymentConfiguration.get_active_config()
            if not payment_config.enable_razorpay:
                return

            credentials = payment_config.get_razorpay_credentials()
            if not credentials['key_id'] or not credentials['key_secret']:
                return

            # Create payment transaction
            transaction = PaymentTransaction.objects.create(
                order_id=str(order.id),
                user=order.customer,
                payment_method='razorpay',
                amount=order.total_amount,
                status='initiated'
            )

            # Initialize Razorpay client
            client = razorpay.Client(auth=(credentials['key_id'], credentials['key_secret']))

            # Create payment link
            payment_link = client.payment_link.create({
                'amount': int(float(order.total_amount) * 100),  # Amount in paise
                'currency': 'INR',
                'accept_partial': False,
                'description': f'Payment for Order {order.order_number}',
                'customer': {
                    'name': order.customer.name,
                    'email': order.customer.email or '',
                    'contact': order.customer.mobile_number or ''
                },
                'notify': {
                    'sms': True,
                    'email': True
                },
                'reminder_enable': True,
                'callback_url': f'{payment_config.callback_url}?order_id={order.id}',
                'callback_method': 'get'
            })

            # Update transaction with payment link details
            transaction.gateway_transaction_id = payment_link['id']
            transaction.gateway_response = payment_link
            transaction.save()

            # Store payment link in order admin notes
            payment_link_url = payment_link.get('short_url', payment_link.get('link_url', ''))
            if payment_link_url:
                admin_note = f"Payment link generated: {payment_link_url}"
                if order.admin_notes:
                    order.admin_notes += f"\n{admin_note}"
                else:
                    order.admin_notes = admin_note
                order.save()

        except Exception as e:
            # Log error but don't fail the order creation
            pass


class OrderItemForm(forms.ModelForm):
    """
    Custom form for OrderItem with service selection
    """
    service_selection = forms.ChoiceField(
        choices=[],
        required=False,
        help_text="Select a service to auto-fill details"
    )

    class Meta:
        model = OrderItem
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Populate service choices from catalogue database
        try:
            catalogue_db = connections['catalogue_db']
            with catalogue_db.cursor() as cursor:
                cursor.execute("""
                    SELECT s.id, s.title, s.base_price, s.discount_price, c.name as category_name
                    FROM catalogue_service s
                    JOIN catalogue_category c ON s.category_id = c.id
                    WHERE s.is_active = true
                    ORDER BY c.name, s.title
                """)
                services = cursor.fetchall()

                service_choices = [('', '--- Select Service ---')]
                for service in services:
                    service_id, title, base_price, discount_price, category = service
                    current_price = discount_price if discount_price else base_price
                    choice_label = f"{category} - {title} (₹{current_price})"
                    service_choices.append((service_id, choice_label))

                self.fields['service_selection'].choices = service_choices
        except Exception as e:
            # If catalogue database is not available, disable service selection
            self.fields['service_selection'].choices = [('', 'Service selection unavailable')]
            self.fields['service_selection'].help_text = f"Error loading services: {str(e)}"


class OrderItemInline(admin.TabularInline):
    """
    Enhanced inline admin for order items with service selection.
    """
    model = OrderItem
    form = OrderItemForm
    extra = 1
    readonly_fields = ['total_price']
    fields = [
        'service_selection', 'service_id', 'service_title', 'quantity',
        'unit_price', 'discount_per_unit', 'total_price', 'estimated_duration',
        'special_instructions'
    ]

    class Media:
        js = ('admin/js/order_item_admin.js',)  # We'll create this JS file


class OrderStatusHistoryInline(admin.TabularInline):
    """
    Inline admin for order status history.
    """
    model = OrderStatusHistory
    extra = 0
    readonly_fields = ['timestamp']
    fields = ['previous_status', 'new_status', 'changed_by', 'reason', 'timestamp']


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    """
    Admin interface for Order model with tax configuration support.
    """
    form = OrderAdminForm
    list_display = [
        'order_number', 'customer_info', 'status_badge', 'payment_status_badge',
        'total_amount', 'tax_amount', 'assigned_provider_info', 'payment_link_status',
        'scheduled_date', 'created_at'
    ]
    list_filter = [
        'status', 'payment_status', 'payment_method', 'created_at',
        'scheduled_date', 'assigned_provider'
    ]
    search_fields = [
        'order_number', 'customer__mobile_number', 'customer__email',
        'assigned_provider__mobile_number', 'coupon_code'
    ]
    readonly_fields = [
        'id', 'order_number', 'created_at', 'updated_at',
        'confirmed_at', 'completed_at', 'cancelled_at', 'get_tax_breakdown'
    ]
    inlines = [OrderItemInline, OrderStatusHistoryInline]

    class Media:
        css = {
            'all': ('admin/css/order_admin.css',)
        }
        js = ('admin/js/order_item_admin.js',)
    
    fieldsets = (
        ('Order Information', {
            'fields': ('id', 'order_number', 'customer', 'status', 'created_at')
        }),
        ('Order Configuration', {
            'fields': (
                'apply_coupon', 'auto_calculate_tax', 'generate_payment_link'
            ),
            'classes': ('wide',),
            'description': 'Configure order settings: apply coupons, enable auto-tax calculation, and generate payment links'
        }),
        ('Payment Details', {
            'fields': (
                'payment_status', 'payment_method', 'payment_id', 'payment_signature'
            )
        }),
        ('Financial Summary', {
            'fields': (
                'subtotal', 'tax_amount', 'discount_amount', 'minimum_order_fee',
                'total_amount', 'coupon_code', 'coupon_discount', 'get_tax_breakdown'
            )
        }),
        ('Tax Configuration', {
            'fields': (
                'tax_configuration', 'override_tax_calculation',
                'manual_tax_amount', 'tax_exemption_reason'
            ),
            'classes': ('collapse',),
            'description': 'Advanced tax settings. Override automatic calculations if needed.'
        }),
        ('Service Details', {
            'fields': (
                'assigned_provider', 'scheduled_date', 'scheduled_time_slot',
                'delivery_address'
            )
        }),
        ('Notes', {
            'fields': ('customer_notes', 'admin_notes')
        }),
        ('Timestamps', {
            'fields': ('updated_at', 'confirmed_at', 'completed_at', 'cancelled_at'),
            'classes': ('collapse',)
        }),
    )
    
    def customer_info(self, obj):
        """Display customer information with link"""
        name = obj.customer.name or obj.customer.mobile_number or obj.customer.email
        url = reverse('admin:authentication_user_change', args=[obj.customer.id])
        return format_html('<a href="{}">{}</a>', url, name)
    customer_info.short_description = 'Customer'
    
    def assigned_provider_info(self, obj):
        """Display assigned provider information"""
        if obj.assigned_provider:
            name = obj.assigned_provider.name or obj.assigned_provider.mobile_number or obj.assigned_provider.email
            url = reverse('admin:authentication_user_change', args=[obj.assigned_provider.id])
            return format_html('<a href="{}">{}</a>', url, name)
        return "Not assigned"
    assigned_provider_info.short_description = 'Provider'
    
    def status_badge(self, obj):
        """Display status with color coding"""
        colors = {
            'pending': '#ffc107',
            'confirmed': '#17a2b8',
            'assigned': '#6f42c1',
            'in_progress': '#fd7e14',
            'completed': '#28a745',
            'cancelled': '#dc3545',
            'refunded': '#6c757d'
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; '
            'border-radius: 3px; font-size: 11px; font-weight: bold;">{}</span>',
            color, obj.get_status_display()
        )
    status_badge.short_description = 'Status'
    
    def payment_status_badge(self, obj):
        """Display payment status with color coding"""
        colors = {
            'pending': '#ffc107',
            'paid': '#28a745',
            'failed': '#dc3545',
            'refunded': '#6c757d'
        }
        color = colors.get(obj.payment_status, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; '
            'border-radius: 3px; font-size: 11px; font-weight: bold;">{}</span>',
            color, obj.get_payment_status_display()
        )
    payment_status_badge.short_description = 'Payment'

    def payment_link_status(self, obj):
        """Display payment link status"""
        if obj.payment_method == 'cod':
            # Check if payment link was generated in admin notes
            if obj.admin_notes and 'Payment link generated:' in obj.admin_notes:
                return format_html(
                    '<span style="color: #17a2b8;">COD + Link Available</span>'
                )
            else:
                return format_html(
                    '<span style="color: #28a745;">COD Only</span>'
                )
        elif obj.payment_method == 'razorpay':
            return format_html(
                '<span style="color: #6f42c1;">Online Payment</span>'
            )
        else:
            return format_html(
                '<span style="color: #6c757d;">Unknown</span>'
            )
    payment_link_status.short_description = 'Payment Method'

    def get_tax_breakdown(self, obj):
        """Display detailed tax breakdown for this order"""
        try:
            from taxation.models import TaxCalculation

            # Get the latest tax calculation for this order
            tax_calc = TaxCalculation.objects.filter(
                reference_type='order',
                reference_id=obj.id
            ).first()

            if tax_calc:
                breakdown = f"""
                <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace;">
                    <strong>Tax Breakdown:</strong><br>
                    Subtotal: ₹{tax_calc.subtotal}<br>
                    CGST: ₹{tax_calc.cgst_amount}<br>
                    SGST: ₹{tax_calc.sgst_amount}<br>
                    IGST: ₹{tax_calc.igst_amount}<br>
                    Service Charge: ₹{tax_calc.service_charge}<br>
                    <strong>Total Tax: ₹{tax_calc.total_tax}</strong><br>
                    <strong>Grand Total: ₹{tax_calc.total_amount}</strong>
                </div>
                """
                return format_html(breakdown)
            else:
                return format_html(
                    '<div style="color: #dc3545;">No tax calculation found for this order</div>'
                )
        except ImportError:
            return "Tax module not available"
    get_tax_breakdown.short_description = 'Tax Breakdown'


@admin.register(OrderItem)
class OrderItemAdmin(admin.ModelAdmin):
    """
    Admin interface for OrderItem model.
    """
    list_display = [
        'order_number', 'service_title', 'quantity', 'unit_price',
        'discount_per_unit', 'total_price', 'estimated_duration'
    ]
    list_filter = ['order__status', 'service_id']
    search_fields = [
        'order__order_number', 'service_title', 'order__customer__mobile_number'
    ]
    readonly_fields = ['total_price']

    def order_number(self, obj):
        return obj.order.order_number
    order_number.short_description = 'Order Number'


@admin.register(OrderStatusHistory)
class OrderStatusHistoryAdmin(admin.ModelAdmin):
    """
    Admin interface for OrderStatusHistory model.
    """
    list_display = [
        'order_number', 'previous_status', 'new_status',
        'changed_by_info', 'timestamp'
    ]
    list_filter = ['new_status', 'previous_status', 'timestamp']
    search_fields = ['order__order_number', 'changed_by__mobile_number', 'reason']
    readonly_fields = ['timestamp']
    
    def order_number(self, obj):
        return obj.order.order_number
    order_number.short_description = 'Order Number'
    
    def changed_by_info(self, obj):
        if obj.changed_by:
            return obj.changed_by.name or obj.changed_by.mobile_number or obj.changed_by.email
        return "System"
    changed_by_info.short_description = 'Changed By'


@admin.register(OrderCancellation)
class OrderCancellationAdmin(admin.ModelAdmin):
    """
    Admin interface for OrderCancellation model.
    """
    list_display = [
        'order_number', 'reason', 'cancelled_by_info',
        'refund_amount', 'refund_processed', 'cancelled_at'
    ]
    list_filter = ['reason', 'refund_processed', 'cancelled_at']
    search_fields = ['order__order_number', 'cancelled_by__mobile_number', 'description']
    readonly_fields = ['cancelled_at']
    
    def order_number(self, obj):
        return obj.order.order_number
    order_number.short_description = 'Order Number'
    
    def cancelled_by_info(self, obj):
        if obj.cancelled_by:
            return obj.cancelled_by.name or obj.cancelled_by.mobile_number or obj.cancelled_by.email
        return "System"
    cancelled_by_info.short_description = 'Cancelled By'


@admin.register(OrderReschedule)
class OrderRescheduleAdmin(admin.ModelAdmin):
    """
    Admin interface for OrderReschedule model.
    """
    list_display = [
        'order_number', 'original_date', 'new_date',
        'requested_by_info', 'approved', 'approved_by_info', 'created_at'
    ]
    list_filter = ['approved', 'original_date', 'new_date', 'created_at']
    search_fields = ['order__order_number', 'requested_by__mobile_number', 'reason']
    readonly_fields = ['created_at']
    
    def order_number(self, obj):
        return obj.order.order_number
    order_number.short_description = 'Order Number'
    
    def requested_by_info(self, obj):
        if obj.requested_by:
            return obj.requested_by.name or obj.requested_by.mobile_number or obj.requested_by.email
        return "Unknown"
    requested_by_info.short_description = 'Requested By'

    def approved_by_info(self, obj):
        if obj.approved_by:
            return obj.approved_by.name or obj.approved_by.mobile_number or obj.approved_by.email
        return "Not approved"
    approved_by_info.short_description = 'Approved By'


# Customize admin site headers
admin.site.site_header = "Home Services - Order Management"
admin.site.site_title = "Order Admin"
admin.site.index_title = "Order & Booking Management"
